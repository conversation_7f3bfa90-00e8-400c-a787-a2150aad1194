/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.amap.sales.data.application.executor.downloadhandler.downloadconfig;

import java.util.List;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version $Id: DownloadHeaderConfig.java, v 0.1 2024-09-13 下午2:44 hujin Exp $$
 */
public enum DownloadHeaderXibaoFoodConfigEnum {

    ds("ds", "日期"),

    shop_id("shop_id", "店铺ID"),

    shop_name("shop_name", "店铺名称"),

    city_name("city_name", "城市名称"),

    district_name("district_name", "区域名称"),

    exposure_pv("exposure_pv", "门店曝光量"),

    //exposure_pv_last_cycle("exposure_pv_last_cycle", "门店曝光量环比"),

    store_view_pv("store_view_pv", "门店访问量"),

    //store_view_pv_last_cycle("store_view_pv_last_cycle", "门店访问量环比"),

    total_route_pv("total_route_pv", "规划到店量"),

    //total_route_pv_last_cycle("total_route_pv_last_cycle", "规划到店量环比"),

    ticket_order_cnt("ticket_order_cnt", "预约到店量"),

    //ticket_order_cnt_last_cycle("ticket_order_cnt_last_cycle", "预约到店量环比"),

    phone_order_cnt("phone_order_cnt", "电话拨打量"),
    //phone_order_cnt_last_cycle("phone_order_cnt_last_cycle", "电话拨打量环比"),

    online_order_cnt("online_order_cnt", "在线总预订量"),
    //online_order_cnt_last_cycle("online_order_cnt_last_cycle", "在线总预订量环比"),

    phone_order_rate("phone_order_rate", "电话接通率"),
    //phone_order_rate_last_cycle("phone_order_rate_last_cycle", "电话接通率环比"),

    booking_order_rate("booking_order_rate", "在线订座接单率"),

    //booking_order_rate_last_cycle("booking_order_rate_last_cycle", "在线订座接单率环比"),

    hx_order_rate("hx_order_rate", "商品核销率"),
    //hx_order_rate_last_cycle("hx_order_rate_last_cycle", "商品核销率环比"),

    all_order_cnt("all_order_cnt", "商品下单量"),
    //all_order_cnt_last_cycle("all_order_cnt_last_cycle", "商品下单量环比"),

    group_order_cnt("group_order_cnt", "团单下单量"),
    //group_order_cnt_last_cycle("group_order_cnt_last_cycle", "团单下单量环比"),

    pay_order_cnt("pay_order_cnt", "到店买单量"),
    //pay_order_cnt_last_cycle("pay_order_cnt_last_cycle", "到店买单量环比"),

    pickup_order_cnt("pickup_order_cnt", "自提订单量"),
    //pickup_order_cnt_last_cycle("pickup_order_cnt_last_cycle", "自提订单量环比"),

    ad_cost("ad_cost", "广告总消耗"),
    //ad_cost_last_cycle("ad_cost_last_cycle", "广告总消耗环比"),

    arrive_poi_cnt("arrive_poi_cnt", "广告有效到店量"),
    //arrive_poi_cnt_last_cycle("arrive_poi_cnt_last_cycle", "广告有效到店量环比"),

    settle_cost_rate("settle_cost_rate", "广告有效到店成本"),
    //settle_cost_rate_last_cycle("settle_cost_rate_last_cycle", "广告有效到店成本环比"),

    ad_exp_pv("ad_exp_pv", "广告曝光量"),
    //ad_exp_pv_last_cycle("ad_exp_pv_last_cycle", "广告曝光量环比"),

    ad_clk_pv("ad_clk_pv", "广告点击量"),
    //ad_clk_pv_last_cycle("ad_clk_pv_last_cycle", "广告点击量环比"),

    ad_clk_pv_rate("ad_clk_pv_rate", "广告点击率"),
    //ad_clk_pv_rate_last_cycle("ad_clk_pv_rate_last_cycle", "广告点击率环比"),

    ad_cost_rate("ad_cost_rate", "广告预约到店成本"),
    //ad_cost_rate_last_cycle("ad_cost_rate_last_cycle", "广告预约到店成本环比"),

    // 修改智能体相关字段，与 datas.ts 保持一致
    entry_exp_uv("entry_exp_uv", "智能体曝光量"),

    entry_clk_uv("entry_clk_uv", "智能体使用量"),

    leads_card_exp_uv("leads_card_exp_uv", "留资卡片"),

    serve_card_exp_uv("serve_card_exp_uv", "服务卡片"),

    msg_cnt("msg_cnt", "智能体会话量"),

    msg_cnt_rate("msg_cnt_rate", "平均有效对话轮次"),

    arrive_20m_uv("arrive_20m_uv", "引导到店数"),

    voice_msg_cnt("voice_msg_cnt", "有效播报次数"),

    leads_serve_card_exp_uv("leads_serve_card_exp_uv", "智能体导购次数"),

    arrive_20m_uv_rate("arrive_20m_uv_rate","引导到店占有率"),

    ai_leads_card_exp_uv_rate("ai_leads_card_exp_uv_rate","智能体留资占比"),

    is_aiagent_online("is_aiagent_online", "是否智能体在约"),

    ad_kz_cnt("ad_kz_cnt", "有效客资量"),

    platform_leads_cnt("platform_leads_cnt", "平台客资"),

    wp_online_days("wp_online_days", "防干扰（天）"),

    wp_offline_days("wp_offline_days", "断约天数"),

    wp_online_days_total("wp_online_days_total", "累计年费在约（天）"),

    security_guards_cnt("security_guards_cnt", "信息安全守护（次）"),

    wp_online_remaining_days("wp_online_remaining_days", "权益剩余天数"),

    is_wp_online("is_wp_online", "门店是否在约"),

    call_phone_cnt("call_phone_cnt", "电话拨打量"),

    shop_pic_cnt("shop_pic_cnt", "商家相册数"),

    shop_dish_cnt("shop_dish_cnt", "商家招牌菜数"),

    anti_hijacking_cnt("anti_hijacking_cnt", "防截流次数"),

    business_score("business_score", "商家质量分"),

    fst_sign_dt("fst_sign_dt", "首次签约日期"),

    right_label("right_label", "V标"),

    comment_cnt_std("comment_cnt_std", "累计评论数"),

    optimal_comment_cnt_std("optimal_comment_cnt_std", "累计优质评论数"),

    ngtv_comment_cnt_std("ngtv_comment_cnt_std", "累计差评"),

    ;

    /**
     * code
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    DownloadHeaderXibaoFoodConfigEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<DownloadHeaderXibaoFoodConfigEnum> getAll(){
        return Lists.newArrayList(values());
    }

    /**
     * 根据操作类型code获取操作类型枚举
     *
     * @param code 操作类型code
     * @return 操作类型枚举
     */
    public static DownloadHeaderXibaoFoodConfigEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (DownloadHeaderXibaoFoodConfigEnum activityType : values()) {
            if (StringUtils.equals(activityType.getCode(), code)) {
                return activityType;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}