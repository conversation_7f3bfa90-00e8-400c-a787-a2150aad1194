/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.amap.sales.data.application.executor.downloadhandler.downloadconfig;

import java.util.List;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version $Id: DownloadHeaderXibaoOtherConfig.java, v 0.1 2024-09-13 下午3:01 hujin Exp $$
 */
public enum DownloadHeaderXibaoOtherConfigEnum {

    ds("ds", "日期"),

    shop_id("shop_id", "店铺ID"),

    shop_name("shop_name", "店铺名称"),

    city_name("city_name", "城市名称"),

    district_name("district_name", "区域名称"),

    exposure_pv("exposure_pv", "门店曝光量"),

    //exposure_pv_last_cycle("exposure_pv_last_cycle", "门店曝光量环比"),

    store_view_pv("store_view_pv", "门店访问量"),

    //store_view_pv_last_cycle("store_view_pv_last_cycle", "门店访问量环比"),

    total_route_pv("total_route_pv", "规划到店量"),

    //total_route_pv_last_cycle("total_route_pv_last_cycle", "规划到店量环比"),

    item_cnt("item_cnt", "商品数"),

    //item_cnt_last_cycle("item_cnt_last_cycle", "商品数环比"),

    ord_cnt("ord_cnt", "成交订单数"),

    //ord_cnt_last_cycle("ord_cnt_last_cycle", "成交订单数环比"),

    gmv("gmv", "成交金额"),

    //gmv_last_cycle("gmv_last_cycle", "成交金额环比"),

    //use_gmv_last_cycle("use_gmv_last_cycle", "核销金额环比"),

    use_gmv("use_gmv", "核销金额"),

    use_suborder_cn("use_suborder_cn", "核销笔数"),

    //use_suborder_cn_last_cycle("use_suborder_cn_last_cycle", "核销笔数环比"),

    comment_cnt("comment_cnt", "新增评论数"),

    //comment_cnt_last_cycle("comment_cnt_last_cycle", "新增评论数环比"),

    ad_exp_pv("ad_exp_pv", "广告曝光量"),

    //ad_exp_pv_last_cycle("ad_exp_pv_last_cycle", "广告曝光量环比"),

    ad_exp_pv_per_day("ad_exp_pv_per_day", "广告日均曝光量"),

    //ad_exp_pv_per_day_last_cycle("ad_exp_pv_per_day_last_cycle", "广告日均曝光量环比"),

    ad_clk_pv("ad_clk_pv", "广告点击量"),

    //ad_clk_pv_last_cycle("ad_clk_pv_last_cycle", "广告点击量环比"),

    //ad_clk_pv_rate_last_cycle("ad_clk_pv_rate_last_cycle", "广告点击率环比"),

    ad_clk_pv_rate("ad_clk_pv_rate", "广告点击率"),

    ad_cust_res_cost_per_thousand("ad_cust_res_cost_per_thousand", "千次曝光成本"),

    //ad_cust_res_cost_per_thousand_last_cycle("ad_cust_res_cost_per_thousand_last_cycle", "千次曝光成本环比"),

    settle_cash_cost_per_day("settle_cash_cost_per_day", "日均现金消耗"),

    //settle_cash_cost_per_day_last_cycle("settle_cash_cost_per_day_last_cycle", "日均现金消耗环比"),

    ad_cost("ad_cost", "广告总消耗"),

    //ad_cost_last_cycle("ad_cost_last_cycle", "广告总消耗环比"),

    settle_cash_cost("settle_cash_cost", "现金消耗"),

    //settle_cash_cost_last_cycle("settle_cash_cost_last_cycle", "现金消耗环比"),

    redp_cost("redp_cost", "红包消耗"),

    //redp_cost_last_cycle("redp_cost_last_cycle", "红包消耗环比"),

    ocpc_cost("ocpc_cost", "OCPC客资消耗"),

    //ocpc_cost_last_cycle("ocpc_cost_last_cycle", "OCPC客资消耗环比"),

    ocpc_cost_rate("ocpc_cost_rate", "OCPC客资消耗占比"),

    //ocpc_cost_rate_last_cycle("ocpc_cost_rate_last_cycle", "OCPC客资消耗占比环比"),

    ad_cust_res_cnt_rate("ad_cust_res_cnt_rate", "进店留资率"),

    //ad_cust_res_cnt_rate_last_cycle("ad_cust_res_cnt_rate_last_cycle", "进店留资率环比"),

    ad_kz_cnt_per_day("ad_kz_cnt_per_day", "日均有效客资量"),

    //ad_kz_cnt_per_ day_last_cycle("ad_kz_cnt_per_day_last_cycle", "广告日均有效客资量环比"),

    settle_cash_cost_rate("settle_cash_cost_rate", "有效客资成本"),

    //settle_cash_cost_rate_last_cycle("settle_cash_cost_rate_last_cycle", "有效客资成本环比"),

    ad_kz_cnt("ad_kz_cnt", "有效客资量"),

    //ad_kz_cnt_last_cycle("ad_kz_cnt_last_cycle", "有效客资量环比"),

    call_cust_res_cnt("call_cust_res_cnt", "电话客资"),

    //call_cust_res_cnt_last_cycle("call_cust_res_cnt_last_cycle", "电话客资环比"),

    call_cust_res_cnt_rate("call_cust_res_cnt_rate", "电话客资占比"),

    //call_cust_cnt_rate_last_cycle("call_cust_res_cnt_rate_last_cycle", "电话客资占比环比"),

    platform_leads_cnt("platform_leads_cnt", "平台客资"),

    //platform_leads_cnt_last_cycle("platform_leads_cnt_last_cycle", "平台客资环比"),

    platform_leads_cnt_rate("platform_leads_cnt_rate", "平台客资占比"),

    //platform_leads_cnt_rate_last_cycle("platform_leads_cnt_rate_last_cycle", "平台客资占比环比"),

    ord_cust_res_cnt("ord_cust_res_cnt", "订单客资"),

    //ord_cust_res_cnt_last_cycle("ord_cust_res_cnt_last_cycle", "订单客资环比"),

    ord_cust_res_cnt_rate("ord_cust_res_cnt_rate", "订单客资占比"),

    //ord_cust_res_cnt_rate_last_cycle("ord_cust_res_cnt_rate_last_cycle", "订单客资占比环比"),

    consult_kz_cnt("consult_kz_cnt", "在线咨询客资"),

    //consult_kz_cnt_last_cycle("consult_kz_cnt_last_cycle", "在线咨询客资环比"),

    consult_kz_cnt_rate("consult_kz_cnt_rate", "在线咨询客资占比"),

    //consult_kz_cnt_rate_last_cycle("consult_kz_cnt_rate_last_cycle", "在线咨询客资占比环比"),

    reservation_cust_res_cnt("reservation_cust_res_cnt", "预约礼客资"),

    //reservation_cust_res_cnt_last_cycle("reservation_cust_res_cnt_last_cycle", "预约礼客资环比"),

    reservation_cust_res_cnt_rate("reservation_cust_res_cnt_rate", "预约礼客资占比"),

    //reservation_cust_res_cnt_rate_last_cycle("reservation_cust_res_cnt_rate_last_cycle", "预约礼客资占比环比"),

    arrive_kz_cnt("arrive_kz_cnt", "到店预约客资"),

    //arrive_kz_cnt_last_cycle("arrive_kz_cnt_last_cycle", "到店预约客资环比"),

    arrive_kz_cnt_rate("arrive_kz_cnt_rate", "到店预约客资占比"),

    //arrive_kz_cnt_rate_last_cycle("arrive_kz_cnt_rate_last_cycle", "到店预约客资占比环比"),

    gd_car_kz_cnt("gd_car_kz_cnt", "高德打车客资"),

    //gd_car_kz_cnt_last_cycle("gd_car_kz_cnt_last_cycle", "高德打车客资环比"),

    gd_car_kz_cnt_rate("gd_car_kz_cnt_rate", "高德打车客资占比"),

    //gd_car_kz_cnt_rate_last_cycle("gd_car_kz_cnt_rate_last_cycle", "高德打车客资占比环比"),

    invisible_kz_cnt("invisible_kz_cnt", "不可见客资"),

    //invisible_kz_cnt_last_cycle("invisible_kz_cnt_last_cycle", "不可见客资环比"),

    invisible_kz_cnt_rate("invisible_kz_cnt_rate", "不可见客资占比"),

    //invisible_kz_cnt_rate_last_cycle("invisible_kz_cnt_rate_last_cycle", "不可见客资占比环比"),

    ad_kz_cnt_per_city("ad_kz_cnt_per_city", "分城市客资量占比"),

    //ad_kz_cnt_per_city_last_cycle("ad_kz_cnt_per_city_last_cycle", "分城市客资量占比环比"),

    city_ad_kz_cost("city_ad_kz_cost", "分城市客资成本"),

    //city_ad_kz_cost_last_cycle("city_ad_kz_cost_last_cycle", "分城市客资成本环比"),

    ad_kz_cnt_per_district("ad_kz_cnt_per_district", "分区域客资量占比"),

    //ad_kz_cnt_per_district_last_cycle("ad_kz_cnt_per_district_last_cycle", "分区域客资量占比环比"),

    district_ad_kz_cost("district_ad_kz_cost", "分区域客资成本"),

    //district_ad_kz_cost_last_cycle("district_ad_kz_cost_last_cycle", "分区域客资成本环比"),

    cpc_cost("cpc_cost", "CPC到店消耗"),

    //cpc_cost_last_cycle("cpc_cost_last_cycle", "CPC到店消耗环比"),

    cpc_cost_rate("cpc_cost_rate", "CPC到店消耗占比"),

    //cpc_cost_rate_last_cycle("cpc_cost_rate_last_cycle", "CPC到店消耗占比环比"),

    arrive_poi_cnt("arrive_poi_cnt", "广告有效到店量"),

    //arrive_poi_cnt_last_cycle("arrive_poi_cnt_last_cycle", "广告有效到店量环比"),

    settle_cost_rate("settle_cost_rate", "广告有效到店成本"),

    //settle_cost_rate_last_cycle("settle_cost_rate_last_cycle", "广告有效到店成本环比"),

    orig_call_end_cnt("orig_call_end_cnt", "来电量"),

    //orig_call_end_cnt_last_cycle("orig_call_end_cnt_last_cycle", "来电量环比"),

    // 修改智能体相关字段，与 datas.ts 保持一致
    entry_exp_uv("entry_exp_uv", "智能体曝光量"),

    entry_clk_uv("entry_clk_uv", "智能体使用量"),

    leads_card_exp_uv("leads_card_exp_uv", "留资卡片"),

    serve_card_exp_uv("serve_card_exp_uv", "服务卡片"),

    msg_cnt("msg_cnt", "智能体会话量"),

    msg_cnt_rate("msg_cnt_rate", "平均有效对话轮次"),

    arrive_20m_uv("arrive_20m_uv", "引导到店数"),

    voice_msg_cnt("voice_msg_cnt", "有效播报次数"),

    leads_serve_card_exp_uv("leads_serve_card_exp_uv", "智能体导购次数"),

    arrive_20m_uv_rate("arrive_20m_uv_rate","引导到店占有率"),

    ai_leads_card_exp_uv_rate("ai_leads_card_exp_uv_rate","智能体留资占比"),

    is_aiagent_online("is_aiagent_online", "是否智能体在约"),

    wp_online_days("wp_online_days", "防干扰（天）"),

    wp_offline_days("wp_offline_days", "断约天数"),

    wp_online_days_total("wp_online_days_total", "累计年费在约（天）"),

    security_guards_cnt("security_guards_cnt", "信息安全守护（次）"),

    wp_online_remaining_days("wp_online_remaining_days", "权益剩余天数"),

    is_wp_online("is_wp_online", "门店是否在约"),

    call_phone_cnt("call_phone_cnt", "电话拨打量"),

    shop_pic_cnt("shop_pic_cnt", "商家相册数"),

    shop_dish_cnt("shop_dish_cnt", "商家招牌菜数"),

    anti_hijacking_cnt("anti_hijacking_cnt", "防截流次数"),

    business_score("business_score", "商家质量分"),

    fst_sign_dt("fst_sign_dt", "首次签约日期"),

    right_label("right_label", "V标"),

    comment_cnt_std("comment_cnt_std", "累计评论数"),

    optimal_comment_cnt_std("optimal_comment_cnt_std", "累计优质评论数"),

    ngtv_comment_cnt_std("ngtv_comment_cnt_std", "累计差评"),

    ;

    /**
     * code
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    DownloadHeaderXibaoOtherConfigEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<DownloadHeaderXibaoOtherConfigEnum> getAll(){
        return Lists.newArrayList(values());
    }

    /**
     * 根据操作类型code获取操作类型枚举
     *
     * @param code 操作类型code
     * @return 操作类型枚举
     */
    public static DownloadHeaderXibaoOtherConfigEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (DownloadHeaderXibaoOtherConfigEnum activityType : values()) {
            if (StringUtils.equals(activityType.getCode(), code)) {
                return activityType;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}